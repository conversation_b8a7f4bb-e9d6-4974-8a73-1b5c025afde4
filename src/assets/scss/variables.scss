/* BOG Angular Theme Variables */

// ===== PRIMARY THEME COLORS =====
$bog-primary: #2D336B;
$bog-secondary: #41559B;
$bog-accent: #BD3681;

// ===== GRADIENTS =====
$bog-primary-gradient: linear-gradient(133.33deg, #2D336B 16.35%, #41559B 84.17%);
$bog-button-gradient: linear-gradient(90deg, #2D336B 0%, #41559B 52.08%, #BD3681 100%);
$bog-header-gradient: linear-gradient(90deg, #2D336B 0%, #41559B 54.17%, #BD3681 100%);

// ===== BACKGROUND COLORS =====
$bog-main-background: #fafafa;
$bog-sub-background: #ffffff;
$bog-card-background: #ffffff;
$bog-overlay-background: rgba(0, 0, 0, 0.30);

// ===== TEXT COLORS =====
$bog-text-primary: #161616;
$bog-text-secondary: #555555;
$bog-text-muted: #888888;
$bog-text-white: #ffffff;
$bog-text-error: #dc3545;

// ===== BORDER COLORS =====
$bog-border-light: #dfe4ed;
$bog-border-lighter: #e6ebf5;
$bog-border-default: #ced4da;
$bog-border-table: #dfe6ec;

// ===== SHADOW COLORS =====
$bog-shadow-light: #f0f0f0;
$bog-shadow-default: rgba(0, 0, 0, 0.1);
$bog-shadow-medium: rgba(0, 0, 0, 0.2);

// ===== SIDEBAR THEME =====
$bog-sidebar-width: 250px;
$bog-sidebar-collapsed-width: 60px;
$bog-sidebar-bg: $bog-primary-gradient;
$bog-sidebar-text: #ffffff;
$bog-sidebar-active-bg: rgba(255, 255, 255, 0.1);
$bog-sidebar-hover-bg: rgba(255, 255, 255, 0.05);
$bog-sidebar-active-indicator: $bog-primary-gradient;

// ===== TABLE THEME =====
$bog-table-header-bg: $bog-primary;
$bog-table-header-text: #ffffff;
$bog-table-row-hover: rgba(45, 51, 107, 0.05);
$bog-table-border: 1px solid $bog-border-table;
$bog-table-stripe-bg: #f8f9fa;

// ===== BUTTON THEME =====
$bog-btn-primary-bg: $bog-button-gradient;
$bog-btn-primary-text: #ffffff;
$bog-btn-secondary-bg: rgba(45, 51, 107, 0.1);
$bog-btn-secondary-text: $bog-primary;
$bog-btn-border-radius: 8px;
$bog-btn-padding: 10px 15px;

// ===== FORM THEME =====
$bog-form-border: 1px solid $bog-border-default;
$bog-form-border-radius: 6px;
$bog-form-focus-border: $bog-primary;
$bog-form-focus-shadow: 0 0 0 0.2rem rgba(45, 51, 107, 0.25);
$bog-form-error-border: $bog-text-error;

// ===== CARD THEME =====
$bog-card-border-radius: 10px;
$bog-card-shadow: 0 4px 8px $bog-shadow-default;
$bog-card-border: 1px solid rgba(0, 0, 0, 0.125);

// ===== PAGINATION THEME =====
$bog-pagination-bg: #f0f0f0;
$bog-pagination-border: 1px solid #ddd;
$bog-pagination-active-bg: $bog-button-gradient;
$bog-pagination-active-text: #ffffff;
$bog-pagination-hover-bg: rgba(45, 51, 107, 0.1);

// ===== OFFCANVAS THEME =====
$bog-offcanvas-bg: #ffffff;
$bog-offcanvas-border-radius: 15px;
$bog-offcanvas-close-btn-bg: $bog-primary;
$bog-offcanvas-close-btn-text: #ffffff;
$bog-offcanvas-shadow: 0 0 20px rgba(0, 0, 0, 0.3);

// ===== TYPOGRAPHY =====
$bog-font-family: "adani", sans-serif;
$bog-font-size-xs: 10px;
$bog-font-size-sm: 12px;
$bog-font-size-base: 14px;
$bog-font-size-lg: 16px;
$bog-font-size-xl: 18px;
$bog-font-size-xxl: 20px;
$bog-font-weight-normal: 400;
$bog-font-weight-medium: 500;
$bog-font-weight-semibold: 600;
$bog-font-weight-bold: 700;

// ===== SPACING =====
$bog-spacing-xs: 4px;
$bog-spacing-sm: 8px;
$bog-spacing-md: 12px;
$bog-spacing-lg: 16px;
$bog-spacing-xl: 20px;
$bog-spacing-xxl: 24px;

// ===== Z-INDEX =====
$bog-z-index-dropdown: 1000;
$bog-z-index-modal: 1050;
$bog-z-index-offcanvas: 1100;
$bog-z-index-tooltip: 1200;

// ===== LEGACY COMPATIBILITY (to be phased out) =====
$theme1-primary: $bog-primary;
$theme1-main-heading: $bog-text-primary;
$theme1-sub-heading: $bog-text-primary;
$theme1-main-background: $bog-main-background;
$theme1-sub-background: $bog-sub-background;
$theme1-table-data: $bog-text-secondary;
$theme1-shadow: $bog-shadow-light;

$sideBarWidth: $bog-sidebar-width;
$subMenuBg: #1f2d3d;
$subMenuHover: #22272D;
$subMenuActiveText: #1D3C42;
$menuBg: #1D3C42;
$menuText: $bog-sidebar-text;
$menuActiveText: #1D3C42;

$lightGray: #e0e0e0;
$darkGray: #505050;
$loginBg: #808080;
$loginCursorColor: #e0e0e0;
$textAreaBottom: #bfcbd9;

// The :export directive is the magic sauce for webpack
// https://mattferderer.com/use-sass-variables-in-typescript-and-javascript
:export {
  // BOG Theme Colors
  bogPrimary: $bog-primary;
  bogSecondary: $bog-secondary;
  bogAccent: $bog-accent;
  bogPrimaryGradient: $bog-primary-gradient;
  bogButtonGradient: $bog-button-gradient;
  bogHeaderGradient: $bog-header-gradient;
  bogMainBackground: $bog-main-background;
  bogSubBackground: $bog-sub-background;
  bogTextPrimary: $bog-text-primary;
  bogTextSecondary: $bog-text-secondary;
  bogSidebarBg: $bog-primary;
  bogSidebarText: $bog-sidebar-text;

  // Legacy compatibility
  menuBg: $menuBg;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
}
