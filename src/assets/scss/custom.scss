.logininformationarea {
    background: linear-gradient(89.27deg, rgba(45, 51, 107, 0.1) 0.6%, rgba(65, 85, 155, 0.1) 51.03%, rgba(189, 54, 129, 0.1) 99.39%);
    border-radius: 13px;
    padding: 17px 22px;
    height: 100%;
}

.logininformationarea > h4 {
    color: $bog-primary;
    font-size: $bog-font-size-xxl;
    font-weight: $bog-font-weight-semibold;
}

.logininformationarea > p {
    color: $bog-text-primary;
    font-size: $bog-font-size-sm;
    font-weight: $bog-font-weight-normal;
    line-height: 14px;
    margin-bottom: 8px !important;
}

//Profile
.tab-class {
    background: $bog-header-gradient !important;
    padding-left: 20px !important;
}

.bg-color {
    background-color: $bog-main-background;
}

.profile-card {
    background-color: $bog-sub-background;
    border-radius: $bog-card-border-radius;
    padding: 20px;
}

.hint-text {
    padding-left: 5px;
    font-size: 10px;
    font-weight: 700;
    color: red;
}


.position-header {
    top: 0px;
    position: relative;
    width: 100%;
}

.position-footer {
    bottom: 0px;
    position: fixed;
    width: 100%;
}