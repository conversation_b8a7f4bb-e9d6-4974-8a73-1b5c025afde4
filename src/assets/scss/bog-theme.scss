/* BOG Angular Theme Mixins and Utilities */

@import './variables.scss';

// ===== MIXINS =====

// Primary gradient mixin
@mixin bog-primary-gradient {
  background: $bog-primary-gradient;
}

// Button gradient mixin
@mixin bog-button-gradient {
  background: $bog-button-gradient;
  color: $bog-btn-primary-text;
  border: none;
  border-radius: $bog-btn-border-radius;
  padding: $bog-btn-padding;
  font-size: $bog-font-size-sm;
  font-weight: $bog-font-weight-semibold;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(45, 51, 107, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// Card styling mixin
@mixin bog-card {
  background-color: $bog-card-background;
  border-radius: $bog-card-border-radius;
  border: $bog-card-border;
  box-shadow: $bog-card-shadow;
  overflow: hidden;
}

// Table header mixin
@mixin bog-table-header {
  background-color: $bog-table-header-bg !important;
  color: $bog-table-header-text !important;
  font-size: $bog-font-size-sm;
  font-weight: $bog-font-weight-semibold;
  text-align: center;
  padding: $bog-spacing-md;
}

// Table row mixin
@mixin bog-table-row {
  font-size: $bog-font-size-sm;
  font-weight: $bog-font-weight-medium;
  text-align: center;
  padding: $bog-spacing-sm $bog-spacing-md;
  border-bottom: 1px dashed #eee;
  
  &:hover {
    background-color: $bog-table-row-hover;
  }
}

// Form control mixin
@mixin bog-form-control {
  border: $bog-form-border;
  border-radius: $bog-form-border-radius;
  font-family: $bog-font-family;
  font-size: $bog-font-size-sm;
  font-weight: $bog-font-weight-medium;
  
  &:focus {
    border-color: $bog-form-focus-border;
    box-shadow: $bog-form-focus-shadow;
    outline: none;
  }
  
  &.is-invalid {
    border-color: $bog-form-error-border;
  }
}

// Sidebar item mixin
@mixin bog-sidebar-item {
  color: $bog-sidebar-text;
  font-family: $bog-font-family;
  font-size: $bog-font-size-sm;
  font-weight: $bog-font-weight-medium;
  transition: all 0.3s ease-in-out;
  
  &:hover {
    background-color: $bog-sidebar-hover-bg;
  }
  
  &.active {
    background-color: $bog-sidebar-active-bg;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: $bog-sidebar-active-indicator;
      border-radius: 0 2px 2px 0;
    }
  }
}

// Pagination mixin
@mixin bog-pagination-item {
  background-color: $bog-pagination-bg;
  border: $bog-pagination-border;
  color: $bog-text-secondary;
  padding: $bog-spacing-sm $bog-spacing-md;
  text-decoration: none;
  cursor: pointer;
  border-radius: 4px;
  font-size: $bog-font-size-sm;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  
  &:hover {
    background-color: $bog-pagination-hover-bg;
  }
  
  &.active {
    background: $bog-pagination-active-bg;
    color: $bog-pagination-active-text;
    border-color: transparent;
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background-color: $bog-pagination-bg;
    }
  }
}

// Offcanvas mixin
@mixin bog-offcanvas {
  background: $bog-offcanvas-bg;
  border-radius: $bog-offcanvas-border-radius;
  box-shadow: $bog-offcanvas-shadow;
  
  .offcanvas-header {
    background: $bog-header-gradient;
    color: $bog-text-white;
    border-radius: $bog-offcanvas-border-radius $bog-offcanvas-border-radius 0 0;
  }
  
  .close-btn {
    background: $bog-offcanvas-close-btn-bg;
    color: $bog-offcanvas-close-btn-text;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(45, 51, 107, 0.3);
    }
  }
}

// ===== UTILITY CLASSES =====

// Text utilities
.bog-text-primary { color: $bog-text-primary !important; }
.bog-text-secondary { color: $bog-text-secondary !important; }
.bog-text-muted { color: $bog-text-muted !important; }
.bog-text-white { color: $bog-text-white !important; }
.bog-text-error { color: $bog-text-error !important; }

// Background utilities
.bog-bg-primary { background-color: $bog-primary !important; }
.bog-bg-secondary { background-color: $bog-secondary !important; }
.bog-bg-accent { background-color: $bog-accent !important; }
.bog-bg-main { background-color: $bog-main-background !important; }
.bog-bg-sub { background-color: $bog-sub-background !important; }
.bog-bg-card { background-color: $bog-card-background !important; }

// Gradient utilities
.bog-gradient-primary { @include bog-primary-gradient; }
.bog-gradient-button { @include bog-button-gradient; }

// Font utilities
.bog-font-xs { font-size: $bog-font-size-xs !important; }
.bog-font-sm { font-size: $bog-font-size-sm !important; }
.bog-font-base { font-size: $bog-font-size-base !important; }
.bog-font-lg { font-size: $bog-font-size-lg !important; }
.bog-font-xl { font-size: $bog-font-size-xl !important; }
.bog-font-xxl { font-size: $bog-font-size-xxl !important; }

.bog-font-normal { font-weight: $bog-font-weight-normal !important; }
.bog-font-medium { font-weight: $bog-font-weight-medium !important; }
.bog-font-semibold { font-weight: $bog-font-weight-semibold !important; }
.bog-font-bold { font-weight: $bog-font-weight-bold !important; }

// Spacing utilities
.bog-spacing-xs { margin: $bog-spacing-xs !important; }
.bog-spacing-sm { margin: $bog-spacing-sm !important; }
.bog-spacing-md { margin: $bog-spacing-md !important; }
.bog-spacing-lg { margin: $bog-spacing-lg !important; }
.bog-spacing-xl { margin: $bog-spacing-xl !important; }
.bog-spacing-xxl { margin: $bog-spacing-xxl !important; }

// Border utilities
.bog-border-radius { border-radius: $bog-card-border-radius !important; }
.bog-border-light { border: 1px solid $bog-border-light !important; }
.bog-border-default { border: 1px solid $bog-border-default !important; }

// Shadow utilities
.bog-shadow-light { box-shadow: 0 2px 4px $bog-shadow-light !important; }
.bog-shadow-default { box-shadow: $bog-card-shadow !important; }
.bog-shadow-medium { box-shadow: 0 4px 12px $bog-shadow-medium !important; }

// ===== FORM CONTROL STYLES =====

// Input fields
.form-control, input[type="text"], input[type="email"], input[type="password"],
input[type="number"], input[type="tel"], input[type="url"], textarea, select {
  @include bog-form-control;
}

// Select dropdowns
select.form-control, .form-select {
  @include bog-form-control;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}

// Filter specific styling
.filter-container {
  .form-label {
    color: $bog-text-secondary;
    font-size: $bog-font-size-sm;
    font-weight: $bog-font-weight-medium;
    margin-bottom: $bog-spacing-xs;
  }

  .form-control {
    margin-bottom: $bog-spacing-md;
  }

  .btn-primary {
    @include bog-button-gradient;
    width: 100%;
    margin-top: $bog-spacing-lg;
  }
}

// Checkbox and radio styling
.form-check-input {
  border: 2px solid $bog-border-default;

  &:checked {
    background-color: $bog-primary;
    border-color: $bog-primary;
  }

  &:focus {
    border-color: $bog-form-focus-border;
    box-shadow: $bog-form-focus-shadow;
  }
}

// Badge styling for filters
.badge {
  &.bg-primary {
    background-color: $bog-primary !important;
  }

  &.bg-secondary {
    background-color: $bog-secondary !important;
  }

  &.bg-success {
    background-color: #28a745 !important;
  }

  &.bg-warning {
    background-color: #ffc107 !important;
    color: $bog-text-primary !important;
  }
}

// ===== TAB COMPONENTS =====

// Bootstrap nav tabs styling
.nav-tabs {
  border-bottom: 2px solid $bog-border-light;

  .nav-link {
    color: $bog-text-secondary;
    font-size: $bog-font-size-sm;
    font-weight: $bog-font-weight-medium;
    padding: $bog-spacing-md $bog-spacing-lg;
    border: none;
    border-bottom: 3px solid transparent;
    background: transparent;
    transition: all 0.3s ease;

    &:hover {
      color: $bog-primary;
      border-bottom-color: rgba(45, 51, 107, 0.3);
    }

    &.active {
      color: $bog-primary;
      background: transparent;
      border-bottom-color: $bog-primary;
      font-weight: $bog-font-weight-semibold;
    }
  }
}

// Custom tab styling for specific components
.bog-tabs {
  display: flex;
  border-bottom: 2px solid $bog-border-light;
  margin-bottom: $bog-spacing-lg;

  .bog-tab {
    padding: $bog-spacing-md $bog-spacing-xl;
    background: transparent;
    border: none;
    color: $bog-text-secondary;
    font-size: $bog-font-size-sm;
    font-weight: $bog-font-weight-medium;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      color: $bog-primary;
      background: rgba(45, 51, 107, 0.05);
    }

    &.active {
      color: $bog-primary;
      font-weight: $bog-font-weight-semibold;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 3px;
        background: $bog-primary-gradient;
        border-radius: 2px 2px 0 0;
      }
    }
  }
}

// Tab content styling
.tab-content {
  padding: $bog-spacing-lg 0;

  .tab-pane {
    animation: fadeIn 0.3s ease-in-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
