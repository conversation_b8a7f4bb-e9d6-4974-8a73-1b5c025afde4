.mat-mdc-header-row {
    height: 40px !important;
    background: $bog-table-header-bg !important;
    color: $bog-table-header-text !important;
}

.mat-mdc-row {
    height: 40px !important;

    &:hover {
        background-color: $bog-table-row-hover !important;
    }
}

thead th {
    @include bog-table-header;
    font-family: $bog-font-family !important;
}

tbody td {
    @include bog-table-row;
    font-family: $bog-font-family;
}

.table-title {
    font-weight: $bog-font-weight-semibold !important;
    font-family: $bog-font-family;
    color: $bog-text-primary;
}

/* common */
.export, .column-selection, .filter {
    height: 30px;
    width: 30px;
    border: 1px solid $bog-border-default;
    margin-left: 10px;
    background: $bog-sub-background;
    border-radius: 2px;
    transition: all 0.3s ease;
    cursor: pointer;

    img {
        padding: 7px;
    }
}

.export:hover, .column-selection:hover, .filter:hover {
    background: $bog-pagination-hover-bg;
    border-color: $bog-primary;
}

/* Search */
::ng-deep.mat-mdc-form-field-flex, .mat-mdc-form-field-flex {
    height: 30px !important;
}

::ng-deep.mat-mdc-form-field-infix {
    margin-top: -14px !important;
}

::ng-deep.mat-mdc-form-field-subscript-wrapper {
    height: 0px !important;
}

::ng-deep.mat-mdc-text-field-wrapper {
    background: $bog-sub-background;
    border: $bog-form-border;
    border-radius: $bog-form-border-radius;

    &:focus-within {
        border-color: $bog-form-focus-border;
        box-shadow: $bog-form-focus-shadow;
    }
}