.table-container {
    width: 100%;
    overflow-x: auto;
}

.table-responsive {
    overflow-x: auto;
    white-space: nowrap;
    max-width: 100%;
}
table {
    min-width: 1000px; /* Adjust based on the number of columns */
    border-collapse: collapse;
}
.table-header th {
    @include bog-table-header;
}

td{
    @include bog-table-row;
}

i.edit{
    font-size: $bog-font-size-sm;
    color: $bog-primary;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
        color: $bog-secondary;
    }
}
.actions{
    text-align: center !important;
    vertical-align: middle !important;
}
.table-container{
    overflow-x: auto;
}