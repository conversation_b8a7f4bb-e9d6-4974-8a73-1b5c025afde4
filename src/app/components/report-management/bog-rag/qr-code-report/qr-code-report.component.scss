@import '../../../../../assets/scss/imports';

/* custom-table.component.css */

/* Basic Card Styling */
.card {
    @include bog-card;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    margin-top: $bog-spacing-sm;
}

.card-body{
    height: 70vh;
    overflow: auto;
    padding: 1.25rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid $bog-border-light;
    border-top-left-radius: $bog-card-border-radius;
    border-top-right-radius: $bog-card-border-radius;
    font-size: $bog-font-size-sm;
    color: $bog-text-primary;
    font-weight: $bog-font-weight-medium;
}
  
  
/* Custom Table Styling */
.custom-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: $bog-card-border-radius;
    margin-top: $bog-spacing-sm;
    box-shadow: $bog-card-shadow;
    overflow: hidden;
    border: 1px solid $bog-border-table;
}

.custom-table th,
.custom-table td {
    padding: 10px 12px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid $bog-border-table;
    border-right: 1px solid $bog-border-table;
}

.custom-table th {
    @include bog-table-header;
}

.custom-table td {
    @include bog-table-row;
}
  
  /* Remove right border from the last column cells */
  .custom-table tr td:last-child,
  .custom-table tr th:last-child {
    border-right: none;
  }
  /* Remove bottom border from the last row cells */
  .custom-table tbody tr:last-child td,
  .custom-table tbody tr:last-child th {
    border-bottom: none;
  }
  
  /* Responsive Table (optional) */
  .table-responsive {
    overflow-x: auto;
  }