@import 'variables';
@import 'bog-theme';

.sidebar-container {
    padding: $bog-spacing-sm;
    border-radius: $bog-card-border-radius;
    display: flex;
}

#sidebar {
    width: $bog-sidebar-width;
    min-width: $bog-sidebar-collapsed-width;
    overflow-y: auto;
    color: $bog-sidebar-text;
    transition: width 0.3s ease-in-out;
    box-shadow: 2px 0 10px $bog-shadow-medium;
    z-index: 100;
    text-align: left;
    border-radius: $bog-card-border-radius;
    flex-shrink: 0;
    background: $bog-primary-gradient;

    /* Hide scrollbar but keep functionality */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    /* Hide scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
        display: none;
    }
}

/* Collapsed sidebar */
#sidebar.collapsed {
    width: $bog-sidebar-collapsed-width;
}

/* Title text in navigation items */
#sidebar .nav-title {
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
    opacity: 1;
    visibility: visible;
    white-space: nowrap;
}

/* Hide text and submenu icons when collapsed */
#sidebar.collapsed .nav-title {
    opacity: 0;
    visibility: hidden;
    width: 0;
    margin: 0;
    padding: 0;
}

#sidebar.collapsed .submenu-icon {
    display: none;
}

/* Hide submenus when collapsed */
#sidebar.collapsed .submenu {
    display: none;
}

/* List Items */
#sidebar .list-group-item,
#sidebar .nav-link {
    @include bog-sidebar-item;
    background: transparent;
    border: none;
    padding: 15px 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    overflow: hidden;
    position: relative;
}

/* Active state for list items */
#sidebar .list-group-item.active {
    background: $bog-sidebar-active-bg;
    color: $bog-text-white;
    font-weight: $bog-font-weight-medium;
}

/* Parent items with active children */
#sidebar .list-group-item.active .iconStyle {
    color: $bog-text-white;
}

/* Enhanced styling for parent items with active children */
#sidebar .list-group-item.parent-of-active {
    background-color: rgba(45, 51, 107, 0.08);
    border-left: 2px solid rgba(45, 51, 107, 0.5);
    font-weight: $bog-font-weight-medium;
    transition: all 0.3s ease-in-out;
}

#sidebar .list-group-item.parent-of-active .iconStyle {
    color: $bog-primary;
    transform: scale(1.1);
    transition: transform 0.3s ease, color 0.3s ease;
}

.active-indicator {
    width: 5px;
    height: 100%;
    background: $bog-sidebar-active-indicator;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    box-shadow: 0 0 8px rgba(45, 51, 107, 0.5);
    animation: glow 1.5s ease-in-out infinite alternate;
}

.iconStyle {
    margin-right: 4px;
    font-size: 16px;
    min-width: 20px;
    text-align: center;
    transition: margin 0.3s ease-in-out;
}

/* Center icons when sidebar is collapsed */
#sidebar.collapsed .iconStyle {
    margin: 0 auto;
}

/* Navigation item content container */
.nav-item-content {
    width: 100%;
    transition: all 0.3s ease-in-out;
}

#sidebar.collapsed .nav-item-content {
    justify-content: center;
}

/* Special styling for menu items with subitems when collapsed */
#sidebar.collapsed .list-group-item {
    position: relative;
}

#sidebar.collapsed .list-group-item:hover {
    background-color: $bog-sidebar-hover-bg;
}

/* Content margin adjustment */
.content-wrapper {
    margin-top: -12px;
}

/* Connection line between parent and child */
.submenu {
    position: relative;
}

.submenu::before {
    content: '';
    position: absolute;
    top: 0;
    left: -10px;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, rgba(45, 51, 107, 0.2), rgba(65, 85, 155, 0.2));
    border-radius: 1px;
}

/* Enhanced connection for active items */
.parent-of-active + .submenu::before {
    background: linear-gradient(to bottom, rgba(45, 51, 107, 0.5), rgba(65, 85, 155, 0.5));
    width: 3px;
    left: -11px;
}

/* Animation for active indicator glow effect - using opacity for better performance */
@keyframes glow {
    from {
        opacity: 0.7;
    }
    to {
        opacity: 1;
    }
}