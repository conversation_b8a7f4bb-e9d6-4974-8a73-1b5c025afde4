@import '../../../assets/scss/imports';

.card-body{
    height: 70vh;
    overflow: auto;
}

.header-container{
    margin-top: $bog-spacing-sm;
    font-size: $bog-font-size-xxl;
    color: $bog-text-primary;
    font-weight: $bog-font-weight-semibold;
}

.img-fluid{
    height: 100px;
    width: 100px;
}

.table-header th {
    @include bog-table-header;
}

td{
    @include bog-table-row;
}

i.edit{
    font-size: $bog-font-size-sm;
    color: $bog-primary;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
        color: $bog-secondary;
    }
}

.actions{
    text-align: center !important;
    vertical-align: middle !important;
}

.qr-data{
    text-align: center !important;
    vertical-align: middle !important;
}

/* Styling for QR code details */
.details-container {
    display: table;
    width: 100%;
    text-align: left;
    padding: 5px;
    border-spacing: 0 8px;
}
.label-value {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
    align-items: baseline;
    padding: 2px 0;
    border-bottom: 1px dashed #eee;
    padding-bottom: 8px;
}

.label-value:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.label-value strong {
    font-weight: 500;
    color: #777;
    margin-right: 8px;
    min-width: 110px;
    font-size: 12px;
    display: inline-block;
    vertical-align: top;
}

.value-text {
    font-weight: 600;
    color: #222;
    font-size: 12px;
    display: inline-block;
    word-break: break-word;
    overflow-wrap: break-word;
    flex: 1;
    letter-spacing: normal;
    line-height: 1.5;
}

/* Style for bold text in table cells to match value-text */
td b,
td strong,
td span:not(.info-label) {
    font-weight: 600;
    color: #222;
    font-size: 12px;
    letter-spacing: normal;
    line-height: 1.5;
}

/* Add bottom border to table rows */
.table-bordered tbody tr {
    border-bottom: 1px dashed #eee;
}

/* Styling for modal tables */
.modal-body table {
    width: 100%;
    border-collapse: collapse;
}

.modal-body table th {
    font-weight: 600;
    background-color: #f5f7fa;
    color: #333;
    text-align: center;
    padding: 8px;
}

.modal-body table td {
    font-weight: 500;
    padding: 8px;
    border-bottom: 1px dashed #eee;
}

/* Ensure consistent styling for details in modals */
.modal-body .details-container p {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
    align-items: baseline;
    padding: 2px 0;
    border-bottom: 1px dashed #eee;
    padding-bottom: 8px;
}

.modal-body .details-container p:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

