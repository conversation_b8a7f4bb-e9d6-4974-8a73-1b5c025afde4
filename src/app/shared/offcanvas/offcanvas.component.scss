@import 'variables';
@import 'bog-theme';

.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: $bog-overlay-background;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: $bog-z-index-offcanvas;
    opacity: 0;
    animation: fadeIn 0.3s forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.custom-modal-content {
    @include bog-offcanvas;
    max-width: 1100px;
    z-index: $bog-z-index-offcanvas;
    position: fixed;
    right: 0;
    top: 0;
    height: 100%;
    transform: translateX(100%);
    animation: slideIn 0.5s forwards;
    overflow-x: hidden;

    // Rounded corners for top and bottom
    border-top-left-radius: $bog-offcanvas-border-radius;
    border-bottom-left-radius: $bog-offcanvas-border-radius;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

.custom-modal-header {
    background: $bog-header-gradient;
    color: $bog-text-white;
    padding: $bog-spacing-lg $bog-spacing-xl;
    border-radius: $bog-offcanvas-border-radius $bog-offcanvas-border-radius 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    h5 {
        margin: 0;
        font-size: $bog-font-size-lg;
        font-weight: $bog-font-weight-semibold;
        font-family: $bog-font-family;
    }
}

.close-button {
    background: $bog-offcanvas-close-btn-bg;
    color: $bog-offcanvas-close-btn-text;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: $bog-font-size-xl;
    font-weight: $bog-font-weight-bold;
    transition: all 0.3s ease;
    position: absolute;
    left: -20px; // Half outside the offcanvas
    top: 50%;
    transform: translateY(-50%);
    box-shadow: 0 2px 8px rgba(45, 51, 107, 0.3);

    &:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 4px 12px rgba(45, 51, 107, 0.4);
    }

    &:active {
        transform: translateY(-50%) scale(0.95);
    }
}

.custom-modal-body {
    padding: $bog-spacing-xl;
    background: $bog-offcanvas-bg;
    height: calc(100% - 80px); // Adjust based on header height
    overflow-y: auto;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: $bog-primary;
        border-radius: 3px;

        &:hover {
            background: $bog-secondary;
        }
    }
}

.custom-modal-header {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 53px;
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
    padding: 0 20px;
    color: #fff;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #fff;
}

.custom-modal-body {
    height: 100%;
    margin: 20px 10px;
}

.custom-modal-footer {
    text-align: right;
    margin: 0 10px;
}