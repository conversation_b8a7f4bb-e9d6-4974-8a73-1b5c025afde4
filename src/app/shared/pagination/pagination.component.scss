@import '../../../assets/scss/imports';

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: $bog-spacing-xl;
}

.modern-pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: $bog-spacing-xs;
}

.page-item {
    // No margin needed due to gap on parent
}

.page-link {
    @include bog-pagination-item;
}

  .page-item.disabled .page-link {
    background-color: #eee;
    color: #aaa;
    cursor: not-allowed;
    border-color: #ddd;
  }

  .bi {
    font-size: 1rem;
  }
  .pagination-info{
    font-size: 12px;
  }